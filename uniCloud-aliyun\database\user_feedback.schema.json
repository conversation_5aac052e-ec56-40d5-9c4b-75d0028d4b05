{"bsonType": "object", "description": "用户反馈表", "required": ["userId", "taskId", "rating", "createTime"], "properties": {"_id": {"description": "反馈记录ID，系统自动生成"}, "userId": {"bsonType": "string", "description": "用户ID（对应users表的_id）", "title": "用户ID"}, "openid": {"bsonType": "string", "description": "微信用户openid，用于快速查询", "title": "微信OpenID"}, "taskId": {"bsonType": "string", "description": "视频翻译任务ID（对应tasks表的_id）", "title": "任务ID"}, "rating": {"bsonType": "number", "description": "用户评分，1-5星", "title": "评分", "minimum": 1, "maximum": 5}, "feedbackContent": {"bsonType": "string", "description": "用户反馈内容", "title": "反馈内容", "maxLength": 1000}, "feedbackType": {"bsonType": "string", "description": "反馈类型", "title": "反馈类型", "enum": ["general", "bug", "suggestion", "praise", "complaint"], "enumDesc": ["一般反馈", "问题反馈", "建议反馈", "表扬反馈", "投诉反馈"], "default": "general"}, "deviceInfo": {"bsonType": "object", "description": "设备信息", "title": "设备信息", "properties": {"platform": {"bsonType": "string", "description": "平台类型"}, "system": {"bsonType": "string", "description": "操作系统"}, "version": {"bsonType": "string", "description": "系统版本"}, "model": {"bsonType": "string", "description": "设备型号"}, "brand": {"bsonType": "string", "description": "设备品牌"}}}, "appVersion": {"bsonType": "string", "description": "应用版本号", "title": "应用版本"}, "userAgent": {"bsonType": "string", "description": "用户代理字符串", "title": "用户代理"}, "ipAddress": {"bsonType": "string", "description": "用户IP地址", "title": "IP地址"}, "status": {"bsonType": "string", "description": "反馈状态", "title": "状态", "enum": ["pending", "processing", "resolved", "closed"], "enumDesc": ["待处理", "处理中", "已解决", "已关闭"], "default": "pending"}, "adminReply": {"bsonType": "string", "description": "管理员回复", "title": "管理员回复", "maxLength": 2000}, "adminId": {"bsonType": "string", "description": "处理的管理员ID", "title": "管理员ID"}, "processedAt": {"bsonType": "timestamp", "description": "处理时间", "title": "处理时间"}, "createTime": {"bsonType": "timestamp", "description": "创建时间", "title": "创建时间", "forceDefaultValue": {"$env": "now"}}, "updateTime": {"bsonType": "timestamp", "description": "更新时间", "title": "更新时间", "forceDefaultValue": {"$env": "now"}}}, "permission": {"read": "doc.userId == auth.uid", "create": true, "update": "doc.userId == auth.uid", "delete": false}}