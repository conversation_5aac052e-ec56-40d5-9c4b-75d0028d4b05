<template>
  <view class="result-container">
    <!-- 现代化头部区域 -->
    <view class="hero-header">
      <view class="hero-background"></view>
      <view class="hero-content">
        <view class="success-indicator">
          <view class="success-icon">
            <text class="check-icon">✓</text>
            <view class="success-glow"></view>
          </view>
          <view class="success-content">
            <text class="success-title">视频字幕制作完成</text>
            <text class="success-desc">AI智能识别完毕，字幕已精准生成</text>
          </view>
        </view>
        <view class="hero-decoration">
          <view class="decoration-circle circle-1"></view>
          <view class="decoration-circle circle-2"></view>
          <view class="decoration-circle circle-3"></view>
        </view>
      </view>
    </view>

    <!-- 内容卡片区域 -->
    <view class="content-wrapper">
      <!-- 视频预览卡片 -->
      <view class="media-card">
        <view class="card-header">
          <view class="header-content">
            <text class="card-title">视频预览</text>
            <view class="card-badge">
              <text class="badge-text">HD</text>
            </view>
          </view>
          <view class="card-divider"></view>
        </view>
        <view class="video-wrapper">
          <view class="video-container">
            <video v-if="videoUrl" :src="videoUrl" controls class="video-player"></video>
            <view v-else class="video-placeholder">
              <view class="loading-animation">
                <text class="loading-icon">🎬</text>
                <view class="pulse-ring"></view>
              </view>
              <text class="loading-text">视频加载中...</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 字幕内容卡片 -->
      <view class="subtitle-card">
        <view class="card-header">
          <view class="header-content">
            <text class="card-title">字幕内容</text>
            <view class="subtitle-count">
              <text class="count-icon">📝</text>
              <text class="count-text">{{ subtitles.length }}条</text>
            </view>
          </view>
          <view class="card-divider"></view>
        </view>
        <scroll-view class="subtitle-scroll" scroll-y="true">
          <view class="subtitle-list">
            <view class="subtitle-item" v-for="(item, index) in subtitles" :key="index">
              <view class="subtitle-index">
                <text class="index-number">{{ index + 1 }}</text>
              </view>
              <view class="subtitle-content">
                <text class="subtitle-time">{{ item.startTime }} - {{ item.endTime }}</text>
                <text class="subtitle-text">{{ item.text }}</text>
              </view>
            </view>
            <view v-if="subtitles.length === 0" class="empty-subtitle">
              <view class="empty-icon">
                <text class="empty-emoji">📝</text>
              </view>
              <text class="empty-text">暂无字幕内容</text>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 快速反馈卡片 -->
      <view class="feedback-card" v-if="!feedbackSubmitted">
        <view class="card-header">
          <view class="header-content">
            <text class="card-title">您的体验如何？</text>
            <view class="feedback-badge">
              <text class="badge-text">反馈</text>
            </view>
          </view>
          <view class="card-divider"></view>
        </view>
        <view class="feedback-content">
          <view class="quick-feedback-options">
            <view
              class="feedback-option"
              v-for="option in feedbackOptions"
              :key="option.id"
              @click="showFeedbackModal(option)"
            >
              <view class="option-icon">{{ option.icon }}</view>
              <text class="option-text">{{ option.text }}</text>
              <view class="option-arrow">›</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 反馈成功提示卡片 -->
      <view class="feedback-success-card" v-if="feedbackSubmitted">
        <view class="success-content">
          <view class="success-icon-wrapper">
            <text class="success-icon">🎉</text>
            <view class="success-glow"></view>
          </view>
          <text class="success-title">感谢您的反馈！</text>
          <text class="success-desc">您的宝贵意见将帮助我们不断改进服务质量</text>
        </view>
      </view>
    </view>

    <!-- 反馈弹窗 -->
    <view class="feedback-modal" v-if="showModal" @click="closeFeedbackModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">{{ currentFeedbackOption.title }}</text>
          <view class="modal-close" @click="closeFeedbackModal">×</view>
        </view>

        <view class="modal-body">
          <!-- 星级评分 -->
          <view class="rating-section">
            <text class="section-title">请为本次服务评分</text>
            <view class="star-rating">
              <view
                class="star-item"
                v-for="star in 5"
                :key="star"
                @click="setRating(star)"
              >
                <text
                  class="star-icon"
                  :class="{ 'star-active': star <= currentRating, 'star-hover': star <= hoverRating }"
                  @touchstart="setHoverRating(star)"
                  @touchend="clearHoverRating"
                >⭐</text>
              </view>
            </view>
            <text class="rating-text" v-if="currentRating > 0">
              {{ getRatingText(currentRating) }}
            </text>
          </view>

          <!-- 建议输入 -->
          <view class="feedback-input-section">
            <text class="section-title">{{ currentFeedbackOption.inputLabel }}</text>
            <textarea
              class="feedback-textarea"
              v-model="suggestionContent"
              :placeholder="currentFeedbackOption.placeholder"
              maxlength="500"
              :show-confirm-bar="false"
            ></textarea>
            <view class="char-count">
              <text class="count-text">{{ suggestionContent.length }}/500</text>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <button class="cancel-btn" @click="closeFeedbackModal">取消</button>
          <button
            class="submit-btn"
            :class="{ 'btn-disabled': !canSubmit }"
            :disabled="!canSubmit || isSubmitting"
            @click="submitFeedback"
          >
            <text v-if="!isSubmitting">提交反馈</text>
            <text v-else>提交中...</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 底部操作区 -->
    <view class="action-footer">
      <view class="action-buttons">
        <button @click="downloadVideo" class="download-btn">
          <view class="btn-content">
            <text class="btn-icon">💾</text>
            <text class="btn-text">保存到相册</text>
          </view>
          <view class="btn-glow"></view>
        </button>

        <button @click="shareToFriend" class="share-btn">
          <view class="btn-content">
            <text class="btn-icon">📤</text>
            <text class="btn-text">分享给朋友</text>
          </view>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 字幕数据类型定义
interface SubtitleItem {
  startTime: string
  endTime: string
  text: string
}

// 字幕数据
const subtitles = ref<SubtitleItem[]>([])
const taskId = ref('')
const videoUrl = ref('')

// 分享相关数据
const shareTitle = ref('AI智能视频字幕制作完成')
const shareDesc = ref('快来看看我用AI制作的精美视频字幕吧！')

// 反馈功能相关数据
const feedbackSubmitted = ref(false)
const currentRating = ref(0)
const hoverRating = ref(0)
const suggestionContent = ref('')
const isSubmitting = ref(false)
const showModal = ref(false)
const currentFeedbackOption = ref({})

// 反馈选项配置
const feedbackOptions = ref([
  {
    id: 'subtitle_quality',
    icon: '📝',
    text: '对烧录的字幕不满意？',
    title: '字幕质量反馈',
    inputLabel: '请详细描述字幕的问题',
    placeholder: '例如：字幕位置不准确、字体大小不合适、翻译有误等...'
  },
  {
    id: 'video_quality',
    icon: '🎬',
    text: '对视频质量不满意？',
    title: '视频质量反馈',
    inputLabel: '请详细描述视频的问题',
    placeholder: '例如：视频清晰度不够、画面有瑕疵、音画不同步等...'
  },
  {
    id: 'processing_speed',
    icon: '⏱️',
    text: '处理速度太慢？',
    title: '处理速度反馈',
    inputLabel: '请描述您遇到的速度问题',
    placeholder: '例如：等待时间过长、处理卡顿、超时等...'
  },
  {
    id: 'feature_request',
    icon: '💡',
    text: '有功能建议？',
    title: '功能建议',
    inputLabel: '请分享您的想法和建议',
    placeholder: '例如：希望支持更多语言、增加字幕样式选择、批量处理等...'
  },
  {
    id: 'general_feedback',
    icon: '💬',
    text: '其他反馈',
    title: '一般反馈',
    inputLabel: '请分享您的使用体验',
    placeholder: '请告诉我们您的想法和建议...'
  }
])

// 计算属性：是否可以提交反馈
const canSubmit = ref(false)

// 监听评分变化，更新提交按钮状态
const updateCanSubmit = () => {
  canSubmit.value = currentRating.value > 0
}

onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  if (options.taskId) {
    taskId.value = options.taskId

    // 检查是否来自分享
    if (options.from === 'share') {
      uni.showToast({
        title: '欢迎体验AI字幕制作',
        icon: 'none',
        duration: 2000
      })
    } else if (options.from === 'timeline') {
      uni.showToast({
        title: '欢迎体验AI字幕制作',
        icon: 'none',
        duration: 2000
      })
    }

    // 加载处理结果
    loadResult()
  } else {
    uni.showModal({
      title: '参数错误',
      content: '缺少任务ID参数，请重新处理视频',
      showCancel: false,
      success: () => {
        uni.navigateBack()
      }
    })
  }
})

// 加载处理结果
const loadResult = async () => {
  try {
    uni.showLoading({
      title: '加载中...'
    })

    // 调用云函数获取处理结果
    const result = await uniCloud.callFunction({
      name: 'get-task-result',
      data: {
        taskId: taskId.value
      }
    })

    if (result.result.code === 200) {
      const { videoUrl: resultVideoUrl, subtitles: resultSubtitles } = result.result.data

      videoUrl.value = resultVideoUrl || ''
      subtitles.value = resultSubtitles || []

      // 更新分享内容
      updateShareContent()

      uni.hideLoading()
    } else {
      uni.hideLoading()
      uni.showToast({
        title: result.result.message || '加载失败',
        icon: 'none'
      })
    }

  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 检查相册写入权限
const checkWritePhotosAlbumPermission = (): Promise<boolean> => {
  return new Promise((resolve) => {
    uni.getSetting({
      success: (res) => {
        const authSetting = res.authSetting
        if (authSetting['scope.writePhotosAlbum'] === false) {
          // 用户之前拒绝了权限，需要引导用户手动开启
          uni.showModal({
            title: '需要相册权限',
            content: '保存视频需要访问您的相册，请在设置中开启相册权限',
            confirmText: '去设置',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                uni.openSetting({
                  success: (settingRes) => {
                    resolve(settingRes.authSetting['scope.writePhotosAlbum'] === true)
                  },
                  fail: () => resolve(false)
                })
              } else {
                resolve(false)
              }
            }
          })
        } else if (authSetting['scope.writePhotosAlbum'] === undefined) {
          // 用户还没有授权过，直接返回true，让saveVideoToPhotosAlbum触发授权
          resolve(true)
        } else {
          // 用户已经授权
          resolve(true)
        }
      },
      fail: () => resolve(false)
    })
  })
}

// 下载视频
const downloadVideo = async () => {
  if (!videoUrl.value) {
    uni.showToast({
      title: '视频文件不存在',
      icon: 'none'
    })
    return
  }

  try {
    // 先检查相册权限
    const hasPermission = await checkWritePhotosAlbumPermission()
    if (!hasPermission) {
      uni.showToast({
        title: '需要相册权限才能保存视频',
        icon: 'none',
        duration: 3000
      })
      return
    }

    uni.showLoading({
      title: '准备下载...'
    })

    // 下载视频文件
    uni.downloadFile({
      url: videoUrl.value,
      timeout: 60000, // 设置60秒超时
      success: (res) => {
        if (res.statusCode === 200) {
          uni.showLoading({
            title: '保存中...'
          })

          // 保存到相册
          uni.saveVideoToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              uni.hideLoading()
              uni.showToast({
                title: '保存成功',
                icon: 'success'
              })
            },
            fail: (err) => {
              uni.hideLoading()

              // 根据错误类型给出更具体的提示
              let errorMessage = '保存失败'
              if (err.errMsg) {
                if (err.errMsg.includes('auth deny') || err.errMsg.includes('authorize')) {
                  errorMessage = '请授权访问相册后重试'
                } else if (err.errMsg.includes('system error')) {
                  errorMessage = '系统错误，请稍后重试'
                } else if (err.errMsg.includes('file not exist')) {
                  errorMessage = '视频文件无效，请重新生成'
                } else {
                  errorMessage = `保存失败: ${err.errMsg}`
                }
              }

              uni.showModal({
                title: '保存失败',
                content: errorMessage,
                showCancel: true,
                confirmText: '重试',
                cancelText: '取消',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    // 用户选择重试
                    setTimeout(() => {
                      downloadVideo()
                    }, 500)
                  }
                }
              })
            }
          })
        } else {
          uni.hideLoading()
          uni.showToast({
            title: `下载失败 (${res.statusCode})`,
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        uni.hideLoading()

        let errorMessage = '下载失败'
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMessage = '下载超时，请检查网络后重试'
          } else if (err.errMsg.includes('network')) {
            errorMessage = '网络错误，请检查网络连接'
          } else if (err.errMsg.includes('url not in domain list')) {
            errorMessage = '视频链接无效，请重新生成'
          } else {
            errorMessage = `下载失败: ${err.errMsg}`
          }
        }

        uni.showModal({
          title: '下载失败',
          content: errorMessage,
          showCancel: true,
          confirmText: '重试',
          cancelText: '取消',
          success: (modalRes) => {
            if (modalRes.confirm) {
              // 用户选择重试
              setTimeout(() => {
                downloadVideo()
              }, 500)
            }
          }
        })
      }
    })

  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    })
  }
}

// 分享给朋友
const shareToFriend = () => {
  // 触发分享，实际分享内容由 onShareAppMessage 处理
  uni.showModal({
    title: '分享提示',
    content: '请点击右上角的"..."按钮，选择"转发给朋友"或"分享到朋友圈"',
    showCancel: false,
    confirmText: '知道了'
  })
}

// 更新分享内容
const updateShareContent = () => {
  if (subtitles.value.length > 0) {
    shareTitle.value = `AI智能视频字幕制作完成 - ${subtitles.value.length}条字幕`
    shareDesc.value = `快来看看我用AI制作的精美视频字幕吧！共生成了${subtitles.value.length}条精准字幕。`
  }
}

// 反馈功能相关方法
// 设置评分
const setRating = (rating: number) => {
  currentRating.value = rating
  updateCanSubmit()
}

// 设置悬停评分
const setHoverRating = (rating: number) => {
  hoverRating.value = rating
}

// 清除悬停评分
const clearHoverRating = () => {
  hoverRating.value = 0
}

// 显示反馈弹窗
const showFeedbackModal = (option: any) => {
  currentFeedbackOption.value = option
  currentRating.value = 0
  suggestionContent.value = ''
  showModal.value = true
  updateCanSubmit()
}

// 关闭反馈弹窗
const closeFeedbackModal = () => {
  showModal.value = false
  currentFeedbackOption.value = {}
  currentRating.value = 0
  suggestionContent.value = ''
  hoverRating.value = 0
  updateCanSubmit()
}

// 获取评分文本
const getRatingText = (rating: number): string => {
  const ratingTexts = {
    1: '很不满意 😞',
    2: '不满意 😐',
    3: '一般 😊',
    4: '满意 😄',
    5: '非常满意 🤩'
  }
  return ratingTexts[rating as keyof typeof ratingTexts] || ''
}

// 提交反馈
const submitFeedback = async () => {
  if (!canSubmit.value || isSubmitting.value) {
    return
  }

  try {
    isSubmitting.value = true

    // 调用云函数提交反馈
    const result = await uniCloud.callFunction({
      name: 'submit-feedback',
      data: {
        taskId: taskId.value,
        rating: currentRating.value,
        suggestion: suggestionContent.value.trim()
      }
    })

    if (result.result.code === 200) {
      // 提交成功
      feedbackSubmitted.value = true
      closeFeedbackModal()
      uni.showToast({
        title: '反馈提交成功',
        icon: 'success',
        duration: 2000
      })
    } else {
      // 提交失败
      uni.showToast({
        title: result.result.message || '提交失败',
        icon: 'none',
        duration: 3000
      })
    }

  } catch (error) {
    console.error('提交反馈失败：', error)
    uni.showToast({
      title: '提交失败，请稍后重试',
      icon: 'none',
      duration: 3000
    })
  } finally {
    isSubmitting.value = false
  }
}


</script>

<script lang="ts">
// 页面选项，包含分享函数
export default {
  // 分享给朋友
  onShareAppMessage() {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1] as any
    const options = currentPage.options || {}
    const currentTaskId = options.taskId || ''

    // 显示分享成功提示
    setTimeout(() => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    }, 500)

    return {
      title: 'AI智能视频字幕制作完成',
      desc: '快来看看我用AI制作的精美视频字幕吧！',
      path: `/pages/result/result?taskId=${currentTaskId}&from=share`,
      imageUrl: '' // 使用页面截图作为分享图片
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1] as any
    const options = currentPage.options || {}
    const currentTaskId = options.taskId || ''

    // 显示分享成功提示
    setTimeout(() => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    }, 500)

    return {
      title: 'AI智能视频字幕制作完成 - 快来体验吧！',
      query: `taskId=${currentTaskId}&from=timeline`,
      imageUrl: '' // 使用页面截图作为分享图片
    }
  }
}
</script>

<style scoped>
.result-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  padding-bottom: 120rpx;
}

/* 现代化头部区域 */
.hero-header {
  position: relative;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  padding: 60rpx 32rpx 80rpx;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(99, 102, 241, 0.9) 0%, 
    rgba(79, 70, 229, 0.9) 50%, 
    rgba(139, 92, 246, 0.9) 100%);
  opacity: 0.95;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.success-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
}

.success-icon {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.check-icon {
  font-size: 64rpx;
  color: white;
  font-weight: bold;
}

.success-glow {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.1); }
}

.success-content {
  color: white;
}

.success-title {
  display: block;
  font-size: 42rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.success-desc {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.5;
}

.hero-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 120rpx;
  height: 120rpx;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 80rpx;
  height: 80rpx;
  bottom: 20%;
  left: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 60rpx;
  height: 60rpx;
  top: 60%;
  right: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20rpx) rotate(180deg); }
}

/* 内容区域 */
.content-wrapper {
  padding: 32rpx;
  margin-top: -40rpx;
  position: relative;
  z-index: 3;
}

.media-card, .subtitle-card {
  background: white;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(99, 102, 241, 0.08);
  overflow: hidden;
  border: 1rpx solid rgba(99, 102, 241, 0.08);
}

.card-header {
  padding: 32rpx 32rpx 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
}

.card-badge {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.subtitle-count {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: linear-gradient(135deg, #f0f4ff, #e0e7ff);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(99, 102, 241, 0.1);
}

.count-icon {
  font-size: 20rpx;
}

.count-text {
  font-size: 24rpx;
  font-weight: 600;
  color: #6366f1;
}

.card-divider {
  height: 2rpx;
  background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
  margin-bottom: 24rpx;
  border-radius: 2rpx;
}

/* 视频区域 */
.video-wrapper {
  padding: 0 32rpx 32rpx;
}

.video-container {
  aspect-ratio: 16/9;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.video-player {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

.video-placeholder {
  text-align: center;
  color: #6b7280;
}

.loading-animation {
  position: relative;
  margin-bottom: 24rpx;
}

.loading-icon {
  font-size: 64rpx;
  animation: bounce 1.5s infinite;
}

.pulse-ring {
  position: absolute;
  top: -20rpx;
  left: -20rpx;
  right: -20rpx;
  bottom: -20rpx;
  border: 3rpx solid #6366f1;
  border-radius: 50%;
  opacity: 0.3;
  animation: pulse-ring 2s infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10rpx); }
}

@keyframes pulse-ring {
  0% { transform: scale(0.8); opacity: 0.5; }
  100% { transform: scale(1.2); opacity: 0; }
}

.loading-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 字幕区域 */
.subtitle-scroll {
  max-height: 500rpx;
  padding: 0 32rpx 32rpx;
  box-sizing: border-box;
}

.subtitle-list {
  padding: 0;
}

.subtitle-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f1f5f9;
  gap: 20rpx;
}

.subtitle-item:last-child {
  border-bottom: none;
}

.subtitle-index {
  flex-shrink: 0;
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 4rpx;
}

.index-number {
  color: white;
  font-size: 22rpx;
  font-weight: 600;
}

.subtitle-content {
  flex: 1;
  min-width: 0; /* 确保flex item可以收缩 */
  overflow: hidden;
}

.subtitle-time {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.subtitle-text {
  display: block;
  font-size: 28rpx;
  color: #1f2937;
  line-height: 1.6;
  font-weight: 500;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  white-space: normal;
}

.empty-subtitle {
  text-align: center;
  padding: 80rpx 32rpx;
  color: #9ca3af;
}

.empty-icon {
  margin-bottom: 24rpx;
}

.empty-emoji {
  font-size: 64rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 底部操作区 */
.action-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 24rpx 32rpx;
  border-top: 1rpx solid rgba(99, 102, 241, 0.1);
  z-index: 100;
}

.download-btn {
  position: relative;
  width: 100%;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  border-radius: 20rpx;
  padding: 28rpx;
  color: white;
  font-weight: 600;
  font-size: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(99, 102, 241, 0.3);
  overflow: hidden;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  position: relative;
  z-index: 2;
}

.btn-icon {
  font-size: 28rpx;
}

.btn-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.download-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(99, 102, 241, 0.4);
}

/* 分享按钮样式 */
.action-buttons {
  display: flex;
  gap: 16rpx;
}

.share-btn {
  flex: 1;
  background: linear-gradient(135deg, #10b981, #059669);
  border: none;
  border-radius: 20rpx;
  padding: 28rpx;
  color: white;
  font-weight: 600;
  font-size: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(16, 185, 129, 0.3);
}

.share-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(16, 185, 129, 0.4);
}

.download-btn {
  flex: 1;
}

/* 反馈功能样式 */
.feedback-card {
  background: white;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(99, 102, 241, 0.08);
  overflow: hidden;
  border: 1rpx solid rgba(99, 102, 241, 0.08);
}

.feedback-badge {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.feedback-content {
  padding: 32rpx;
}

/* 快速反馈选项样式 */
.quick-feedback-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.feedback-option {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.feedback-option:active {
  transform: scale(0.98);
  background: #f1f5f9;
  border-color: #6366f1;
}

.option-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.option-text {
  flex: 1;
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
}

.option-arrow {
  font-size: 32rpx;
  color: #9ca3af;
  font-weight: bold;
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20rpx;
}

/* 星级评分样式 */
.rating-section {
  margin-bottom: 40rpx;
}

.star-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.star-item {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.star-item:active {
  transform: scale(0.9);
}

.star-icon {
  font-size: 48rpx;
  color: #d1d5db;
  transition: all 0.3s ease;
  filter: grayscale(100%);
}

.star-icon.star-active {
  color: #fbbf24;
  filter: grayscale(0%);
  text-shadow: 0 2rpx 8rpx rgba(251, 191, 36, 0.3);
}

.star-icon.star-hover {
  color: #fbbf24;
  filter: grayscale(0%);
  transform: scale(1.1);
}

.rating-text {
  display: block;
  text-align: center;
  font-size: 26rpx;
  font-weight: 500;
  color: #6366f1;
  background: linear-gradient(135deg, #f0f4ff, #e0e7ff);
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(99, 102, 241, 0.1);
}

/* 反馈输入样式 */
.feedback-input-section {
  margin-bottom: 40rpx;
}

.feedback-textarea {
  width: 100%;
  min-height: 160rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #1f2937;
  line-height: 1.6;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.feedback-textarea:focus {
  border-color: #6366f1;
  background: white;
  box-shadow: 0 0 0 4rpx rgba(99, 102, 241, 0.1);
}

.char-count {
  text-align: right;
  margin-top: 8rpx;
}

.count-text {
  font-size: 24rpx;
  color: #9ca3af;
}



/* 提交按钮样式 */
.feedback-actions {
  margin-top: 40rpx;
}

.feedback-submit-btn {
  position: relative;
  width: 100%;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  border-radius: 20rpx;
  padding: 28rpx;
  color: white;
  font-weight: 600;
  font-size: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(99, 102, 241, 0.3);
  overflow: hidden;
  transition: all 0.3s ease;
}

.feedback-submit-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(99, 102, 241, 0.4);
}

.feedback-submit-btn.btn-disabled {
  background: #d1d5db;
  color: #9ca3af;
  box-shadow: none;
  cursor: not-allowed;
}

.feedback-submit-btn.btn-disabled:active {
  transform: none;
}

.loading-icon {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 反馈成功样式 */
.feedback-success-card {
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  padding: 40rpx 32rpx;
  text-align: center;
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(16, 185, 129, 0.3);
}

.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-icon-wrapper {
  position: relative;
  margin-bottom: 24rpx;
}

.success-icon {
  font-size: 64rpx;
  animation: bounce 1s ease-in-out;
}

.success-glow {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.success-title {
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.success-desc {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.5;
}

/* 反馈弹窗样式 */
.feedback-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 32rpx;
  box-sizing: border-box;
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 0;
  border-bottom: 1rpx solid #f1f5f9;
  margin-bottom: 32rpx;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #9ca3af;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:active {
  background: #f3f4f6;
  color: #6b7280;
}

.modal-body {
  padding: 0 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #f1f5f9;
  margin-top: 32rpx;
}

.cancel-btn {
  flex: 1;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  padding: 24rpx;
  color: #6b7280;
  font-weight: 600;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.cancel-btn:active {
  background: #f1f5f9;
  border-color: #d1d5db;
}

.submit-btn {
  flex: 2;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  border-radius: 16rpx;
  padding: 24rpx;
  color: white;
  font-weight: 600;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: scale(0.98);
}

.submit-btn.btn-disabled {
  background: #d1d5db;
  color: #9ca3af;
}

.submit-btn.btn-disabled:active {
  transform: none;
}
</style>
