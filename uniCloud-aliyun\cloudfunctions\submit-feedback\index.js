// uniCloud云函数：提交用户反馈
"use strict";

/**
 * 提交用户反馈
 *
 * @param {Object} event
 * @param {string} event.taskId - 任务ID（必需）
 * @param {number} event.rating - 评分，1-5星（必需）
 * @param {string} event.feedbackContent - 反馈内容（可选）
 * @param {string} event.feedbackType - 反馈类型（可选，默认为general）
 * @param {string} event.openid - 用户openid（用于权限验证，可选）
 * @param {Object} event.deviceInfo - 设备信息（可选）
 * @returns {Object} 提交结果
 */
exports.main = async (event, context) => {
  try {
    const { 
      taskId, 
      rating, 
      feedbackContent = '', 
      feedbackType = 'general',
      openid,
      deviceInfo = {},
      appVersion = '',
      userAgent = ''
    } = event;

    console.log("submit-feedback 云函数被调用，参数：", { 
      taskId, 
      rating, 
      feedbackType,
      hasContent: !!feedbackContent,
      hasOpenid: !!openid 
    });

    // 参数验证
    if (!taskId) {
      console.error("参数验证失败：缺少taskId参数");
      return {
        code: 400,
        message: "缺少必要参数：taskId",
      };
    }

    if (!rating || typeof rating !== 'number') {
      console.error("参数验证失败：缺少rating参数或格式不正确");
      return {
        code: 400,
        message: "缺少必要参数：rating，或格式不正确",
      };
    }

    // 验证评分范围
    if (rating < 1 || rating > 5) {
      console.error("参数验证失败：rating超出范围");
      return {
        code: 400,
        message: "评分必须在1-5之间",
      };
    }

    // 验证反馈内容长度
    if (feedbackContent && feedbackContent.length > 1000) {
      console.error("参数验证失败：反馈内容过长");
      return {
        code: 400,
        message: "反馈内容不能超过1000个字符",
      };
    }

    // 验证反馈类型
    const validFeedbackTypes = ['general', 'bug', 'suggestion', 'praise', 'complaint'];
    if (feedbackType && !validFeedbackTypes.includes(feedbackType)) {
      console.error("参数验证失败：反馈类型不正确");
      return {
        code: 400,
        message: "反馈类型不正确",
      };
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");
    const usersCollection = db.collection("users");
    const feedbackCollection = db.collection("user_feedback");

    console.log("准备验证任务存在性，taskId：", taskId);

    // 验证任务是否存在
    const taskResult = await tasksCollection.doc(taskId).get();
    if (!taskResult.data || taskResult.data.length === 0) {
      console.error("任务不存在：", taskId);
      return {
        code: 404,
        message: "任务不存在",
      };
    }

    const taskData = taskResult.data[0];
    console.log("任务验证成功，任务状态：", taskData.status);

    // 获取用户信息
    let userId = null;
    let userOpenid = openid;

    if (openid) {
      // 通过openid查找用户
      const userResult = await usersCollection.where({
        openid: openid
      }).get();

      if (userResult.data && userResult.data.length > 0) {
        userId = userResult.data[0]._id;
        console.log("用户验证成功，userId：", userId);
      } else {
        console.log("用户不存在，将创建匿名反馈");
      }
    }

    // 检查是否已经提交过反馈
    const existingFeedbackQuery = {
      taskId: taskId
    };
    
    if (userId) {
      existingFeedbackQuery.userId = userId;
    } else if (userOpenid) {
      existingFeedbackQuery.openid = userOpenid;
    }

    const existingFeedback = await feedbackCollection.where(existingFeedbackQuery).get();
    
    if (existingFeedback.data && existingFeedback.data.length > 0) {
      console.log("用户已经提交过反馈");
      return {
        code: 409,
        message: "您已经提交过反馈，感谢您的参与！",
      };
    }

    // 获取客户端IP地址
    const clientIP = context.CLIENTIP || '';

    // 准备反馈数据
    const feedbackData = {
      taskId: taskId,
      rating: rating,
      feedbackContent: feedbackContent.trim(),
      feedbackType: feedbackType,
      deviceInfo: deviceInfo,
      appVersion: appVersion,
      userAgent: userAgent,
      ipAddress: clientIP,
      status: 'pending',
      createTime: new Date(),
      updateTime: new Date()
    };

    // 如果有用户ID，添加到反馈数据中
    if (userId) {
      feedbackData.userId = userId;
    }
    
    // 如果有openid，添加到反馈数据中
    if (userOpenid) {
      feedbackData.openid = userOpenid;
    }

    console.log("准备插入反馈数据");

    // 插入反馈数据
    const insertResult = await feedbackCollection.add(feedbackData);

    if (insertResult.id) {
      console.log("反馈提交成功，反馈ID：", insertResult.id);
      
      return {
        code: 200,
        message: "反馈提交成功，感谢您的宝贵意见！",
        data: {
          feedbackId: insertResult.id,
          rating: rating,
          feedbackType: feedbackType
        }
      };
    } else {
      console.error("反馈插入失败");
      return {
        code: 500,
        message: "反馈提交失败，请稍后重试",
      };
    }

  } catch (error) {
    console.error("submit-feedback 云函数执行出错：", error);
    
    // 根据错误类型返回不同的错误信息
    if (error.code === 'DATABASE_PERMISSION_ERROR') {
      return {
        code: 403,
        message: "权限不足，无法提交反馈",
      };
    } else if (error.code === 'NETWORK_ERROR') {
      return {
        code: 503,
        message: "网络错误，请检查网络连接后重试",
      };
    } else {
      return {
        code: 500,
        message: "服务器内部错误，请稍后重试",
      };
    }
  }
};
